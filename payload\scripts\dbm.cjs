// 运行：node dbm.cjs

// 依赖检查和自动安装函数
function checkAndInstallDependencies() {
  // 定义各种数据库驱动包
  const databaseDrivers = {
    postgresql: ['pg'],
    mysql: ['mysql2'],
    mariadb: ['mysql2', 'mariadb'],
    sqlite: ['sqlite3'],
    mongodb: ['mongodb'],
    mssql: ['mssql'],
    oracle: ['oracledb']
  }
  
  // 基础必需包
  const basePackages = ['pg'] // 默认支持 PostgreSQL
  
  // 检测项目中使用的数据库类型
  let detectedDrivers = new Set(basePackages)
  
  try {
    // 检查 package.json 中的数据库相关依赖
    const packageJsonPath = require('path').join(process.cwd(), 'package.json')
    if (require('fs').existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(require('fs').readFileSync(packageJsonPath, 'utf8'))
      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies }
      
      // 检测 Payload CMS 数据库适配器
      Object.keys(allDeps).forEach(dep => {
        if (dep.includes('@payloadcms/db-postgres')) {
          databaseDrivers.postgresql.forEach(pkg => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-mysql')) {
          databaseDrivers.mysql.forEach(pkg => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-sqlite')) {
          databaseDrivers.sqlite.forEach(pkg => detectedDrivers.add(pkg))
        } else if (dep.includes('@payloadcms/db-mongodb')) {
          databaseDrivers.mongodb.forEach(pkg => detectedDrivers.add(pkg))
        }
        
        // 检测直接的数据库驱动依赖
        Object.values(databaseDrivers).flat().forEach(driver => {
          if (dep === driver) {
            detectedDrivers.add(driver)
          }
        })
      })
    }
  } catch (error) {
    console.log('⚠️  无法读取 package.json，使用默认配置')
  }
  
  // 检查环境变量中的数据库配置
  try {
    const envPath = require('path').join(process.cwd(), '.env')
    if (require('fs').existsSync(envPath)) {
      const envContent = require('fs').readFileSync(envPath, 'utf8')
      
      if (envContent.includes('mysql://') || envContent.includes('DATABASE_URL=mysql')) {
        databaseDrivers.mysql.forEach(pkg => detectedDrivers.add(pkg))
      }
      if (envContent.includes('postgres://') || envContent.includes('postgresql://')) {
        databaseDrivers.postgresql.forEach(pkg => detectedDrivers.add(pkg))
      }
      if (envContent.includes('sqlite://') || envContent.includes('.db') || envContent.includes('.sqlite')) {
        databaseDrivers.sqlite.forEach(pkg => detectedDrivers.add(pkg))
      }
      if (envContent.includes('mongodb://') || envContent.includes('mongo://')) {
        databaseDrivers.mongodb.forEach(pkg => detectedDrivers.add(pkg))
      }
    }
  } catch (error) {
    console.log('⚠️  无法读取 .env 文件')
  }
  
  console.log(`🔍 检测到需要的数据库驱动: ${Array.from(detectedDrivers).join(', ')}`)
  
  // 安装检测到的驱动包
  for (const pkg of detectedDrivers) {
    try {
      require.resolve(pkg)
      console.log(`✅ 依赖 "${pkg}" 已存在`)
    } catch (error) {
      console.log(`⚠️  依赖 "${pkg}" 未找到，正在安装...`)
      try {
        const { execSync } = require('child_process')
        // 检查是否使用 pnpm
        const packageManager = require('fs').existsSync('pnpm-lock.yaml') ? 'pnpm' : 
                             require('fs').existsSync('yarn.lock') ? 'yarn' : 'npm'
        
        console.log(`📦 使用 ${packageManager} 安装 ${pkg}...`)
        execSync(`${packageManager} ${packageManager === 'yarn' ? 'add' : 'install'} ${pkg}`, { 
          stdio: 'inherit', 
          cwd: process.cwd() 
        })
        console.log(`✅ 依赖 "${pkg}" 安装成功`)
      } catch (installError) {
        console.error(`❌ 安装依赖 "${pkg}" 失败:`, installError.message)
        console.log(`💡 请手动运行: npm install ${pkg}`)
        // 不退出程序，继续尝试其他包
      }
    }
  }
  
  console.log('🎉 依赖检查完成！')
}

// 在导入其他模块之前先检查依赖
checkAndInstallDependencies()

const { Client } = require('pg')
const { execSync } = require('child_process')
const readline = require('readline')
const fs = require('fs')
const path = require('path')

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function colorLog(message, color = 'reset') {
  console.log(colors[color] + message + colors.reset)
}

// 询问用户输入
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// 数据库配置变量 - 默认值
const DB_CONFIG = {
  // PostgreSQL 服务器配置
  host: '127.0.0.1',
  port: 5432,
  superUser: 'postgres',
  superPassword: '',
  defaultDatabase: 'postgres',

  // 目标数据库配置
  databaseName: '', 
  databaseUser: '', 
  databasePassword: '',
}

// 配置文件路径
const CONFIG_FILE_PATH = path.join(__dirname, '.dbm-config.json')

// 读取配置文件
function loadConfig() {
  try {
    if (fs.existsSync(CONFIG_FILE_PATH)) {
      const configData = JSON.parse(fs.readFileSync(CONFIG_FILE_PATH, 'utf8'))
      
      // 更新配置
      Object.keys(configData).forEach(key => {
        if (DB_CONFIG.hasOwnProperty(key)) {
          DB_CONFIG[key] = configData[key]
        }
      })
      
      colorLog('✅ 已从配置文件加载设置', 'green')
      return true
    }
  } catch (error) {
    colorLog(`⚠️  读取配置文件失败: ${error.message}`, 'yellow')
  }
  return false
}

// 保存配置到文件
function saveConfig() {
  try {
    // 创建一个不包含敏感密码的配置副本
    const configToSave = { ...DB_CONFIG }
    
    // 询问是否保存密码
    const saveSensitiveData = process.env.DBM_SAVE_PASSWORDS === 'true'
    
    // 如果不保存敏感数据，则清除密码
    if (!saveSensitiveData) {
      delete configToSave.superPassword
      delete configToSave.databasePassword
    }
    
    fs.writeFileSync(CONFIG_FILE_PATH, JSON.stringify(configToSave, null, 2), 'utf8')
    colorLog('✅ 配置已保存到文件', 'green')
    return true
  } catch (error) {
    colorLog(`⚠️  保存配置文件失败: ${error.message}`, 'yellow')
    return false
  }
}

// 初始化配置
async function initializeConfig() {
  colorLog('\n⚙️  数据库配置初始化', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  
  // 尝试从配置文件加载
  const configLoaded = loadConfig()
  
  // 如果成功加载配置，询问是否使用现有配置
  if (configLoaded) {
    showConfig(false) // 显示当前配置但不显示密码
    
    const useExisting = await askQuestion('\n是否使用现有配置? (Y/n): ')
    if (useExisting.toLowerCase() !== 'n' && useExisting.toLowerCase() !== 'no') {
      return
    }
  }
  
  // 获取服务器配置
  colorLog('\n📡 PostgreSQL 服务器配置:', 'cyan')
  
  const host = await askQuestion(`主机地址 (默认: ${DB_CONFIG.host}): `)
  if (host) DB_CONFIG.host = host
  
  const port = await askQuestion(`端口 (默认: ${DB_CONFIG.port}): `)
  if (port) DB_CONFIG.port = parseInt(port)
  
  const superUser = await askQuestion(`超级用户 (默认: ${DB_CONFIG.superUser}): `)
  if (superUser) DB_CONFIG.superUser = superUser
  
  const superPassword = await askQuestion(`超级用户密码: `)
  if (superPassword) DB_CONFIG.superPassword = superPassword
  
  const defaultDatabase = await askQuestion(`默认数据库 (默认: ${DB_CONFIG.defaultDatabase}): `)
  if (defaultDatabase) DB_CONFIG.defaultDatabase = defaultDatabase
  
  // 获取目标数据库配置
  colorLog('\n🎯 目标数据库配置:', 'cyan')
  
  const databaseName = await askQuestion(`数据库名称 (默认: ${DB_CONFIG.databaseName || '无'}): `)
  if (databaseName) DB_CONFIG.databaseName = databaseName
  
  // 如果数据库名称为空，则提示用户
  if (!DB_CONFIG.databaseName) {
    colorLog('⚠️  未设置目标数据库名称，部分功能可能受限', 'yellow')
  } else {
    // 默认用户名与数据库名相同
    const defaultUser = DB_CONFIG.databaseName
    const databaseUser = await askQuestion(`数据库用户名 (默认: ${defaultUser}): `)
    DB_CONFIG.databaseUser = databaseUser || defaultUser
    
    const databasePassword = await askQuestion(`数据库密码: `)
    if (databasePassword) DB_CONFIG.databasePassword = databasePassword
  }
  
  // 询问是否保存配置
  const saveConfigChoice = await askQuestion('\n是否保存配置? (Y/n): ')
  if (saveConfigChoice.toLowerCase() !== 'n' && saveConfigChoice.toLowerCase() !== 'no') {
    const savePasswords = await askQuestion('是否保存密码? (y/N): ')
    
    // 临时设置环境变量以控制是否保存密码
    if (savePasswords.toLowerCase() === 'y' || savePasswords.toLowerCase() === 'yes') {
      process.env.DBM_SAVE_PASSWORDS = 'true'
    }
    
    saveConfig()
    
    // 清除环境变量
    delete process.env.DBM_SAVE_PASSWORDS
  }
  
  colorLog('\n✅ 配置初始化完成!', 'green')
}

// 显示配置
async function showConfig(showPasswords = false) {
  colorLog('\n📋 当前数据库配置:', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')
  colorLog(`🏠 主机地址: ${DB_CONFIG.host}:${DB_CONFIG.port}`, 'bright')
  colorLog(`👑 超级用户: ${DB_CONFIG.superUser}`, 'bright')
  if (showPasswords && DB_CONFIG.superPassword) {
    colorLog(`🔑 超级用户密码: ${DB_CONFIG.superPassword}`, 'bright')
  }
  colorLog(`🗄️  默认数据库: ${DB_CONFIG.defaultDatabase}`, 'bright')
  
  if (DB_CONFIG.databaseName) {
    colorLog(`🎯 目标数据库: ${DB_CONFIG.databaseName}`, 'bright')
    colorLog(`👤 数据库用户: ${DB_CONFIG.databaseUser}`, 'bright')
    if (showPasswords && DB_CONFIG.databasePassword) {
      colorLog(`🔑 数据库密码: ${DB_CONFIG.databasePassword}`, 'bright')
    }
  } else {
    colorLog(`🎯 目标数据库: 未设置`, 'yellow')
  }
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  // 如果目标数据库已设置，显示连接字符串
  if (DB_CONFIG.databaseName) {
    const connectionString = `postgres://${DB_CONFIG.databaseUser}:${DB_CONFIG.databasePassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.databaseName}`
    colorLog('\n🔗 数据库连接字符串:', 'cyan')
    colorLog(`${connectionString}`, 'yellow')
  }

  // 显示 .env 文件状态
  try {
    const envPath = path.join(process.cwd(), '.env')
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8')
      const currentUri = envContent.match(/DATABASE_URI=(.*)/)

      colorLog('\n📄 .env 文件中的配置:', 'cyan')
      if (currentUri) {
        colorLog(`${currentUri[1]}`, 'yellow')
        
        // 如果目标数据库已设置，比较连接字符串
        if (DB_CONFIG.databaseName) {
          const connectionString = `postgres://${DB_CONFIG.databaseUser}:${DB_CONFIG.databasePassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.databaseName}`
          
          if (currentUri[1] === connectionString) {
            colorLog('✅ .env 配置与当前配置一致', 'green')
          } else {
            colorLog('⚠️  .env 配置与当前配置不一致', 'yellow')
          }
        }
      } else {
        colorLog('❌ 未找到 DATABASE_URI 配置', 'red')
      }
    } else {
      colorLog('\n⚠️  未找到 .env 文件', 'yellow')
    }
  } catch (error) {
    colorLog('❌ 无法读取 .env 文件', 'red')
  }
}

// 显示菜单
function showMenu() {
  console.clear()
  colorLog('╔══════════════════════════════════════════════════════╗', 'cyan')
  colorLog('║                数据库管理工具                        ║', 'cyan')
  colorLog('╠══════════════════════════════════════════════════════╣', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('║  1. 🔍 数据库连接检测                                ║', 'cyan')
  colorLog('║  2. 🚀 数据库初始化                                  ║', 'cyan')
  colorLog('║  3. 🔄 数据库重建 (删除所有数据)                     ║', 'cyan')
  colorLog('║  4. 📦 运行数据库迁移                                ║', 'cyan')
  colorLog('║  5. ⚙️  显示当前配置                                 ║', 'cyan')
  colorLog('║  6. 🗑️  删除指定数据库                               ║', 'cyan')
  colorLog('║  7. 🆕 交互式创建新数据库                            ║', 'cyan')
  colorLog('║  8. ⚙️  修改数据库配置                               ║', 'cyan')
  colorLog('║  0. 🚪 退出                                          ║', 'cyan')
  colorLog('║                                                      ║', 'cyan')
  colorLog('╚══════════════════════════════════════════════════════╝', 'cyan')
  console.log()
}

// 数据库连接检测
async function testConnection() {
  colorLog('\n🔍 正在检测数据库连接...', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 数据库连接成功！', 'green')

    // 检查目标数据库是否存在
    const result = await client.query(`SELECT 1 FROM pg_database WHERE datname = $1`, [
      DB_CONFIG.databaseName,
    ])

    if (result.rows.length > 0) {
      colorLog(`✅ 数据库 "${DB_CONFIG.databaseName}" 已存在`, 'green')
    } else {
      colorLog(`⚠️  数据库 "${DB_CONFIG.databaseName}" 不存在`, 'yellow')
    }

    // 检查数据库用户是否存在
    const userResult = await client.query(`SELECT 1 FROM pg_user WHERE usename = $1`, [
      DB_CONFIG.databaseUser,
    ])

    if (userResult.rows.length > 0) {
      colorLog(`✅ 数据库用户 "${DB_CONFIG.databaseUser}" 已存在`, 'green')
    } else {
      colorLog(`⚠️  数据库用户 "${DB_CONFIG.databaseUser}" 不存在`, 'yellow')
    }

    // 列出所有 Payload 数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname LIKE 'payload_%' 
      ORDER BY datname
    `)

    if (allDbResult.rows.length > 0) {
      colorLog('\n📋 发现的 Payload 数据库:', 'cyan')
      allDbResult.rows.forEach((row, index) => {
        const dbName = row.datname
        const isCurrent = dbName === DB_CONFIG.databaseName
        colorLog(
          `   ${(index + 1).toString().padStart(2)}. ${dbName}${isCurrent ? ' 👈 当前配置' : ''}`,
          isCurrent ? 'yellow' : 'bright',
        )
      })
    }
  } catch (error) {
    colorLog(`❌ 数据库连接失败: ${error.message}`, 'red')
    return false
  } finally {
    await client.end()
  }

  return true
}

// 数据库初始化
async function initializeDatabase() {
  colorLog('\n🚀 数据库初始化', 'cyan')
  colorLog('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'cyan')

  // 检查配置是否完整
  if (!DB_CONFIG.host || !DB_CONFIG.superUser || !DB_CONFIG.superPassword) {
    colorLog('❌ 数据库配置不完整，请先完成配置', 'red')
    return
  }

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()
    colorLog('✅ 已连接到 PostgreSQL', 'green')
    
    // 列出所有数据库
    const allDbResult = await client.query(`
      SELECT datname 
      FROM pg_database 
      WHERE datname NOT IN ('template0', 'template1', 'postgres')
      ORDER BY datname
    `)
    
    colorLog('\n📋 服务器上的数据库列表:', 'cyan')
    if (allDbResult.rows.length === 0) {
      colorLog('没有找到任何用户数据库', 'yellow')
    } else {
      allDbResult.rows.forEach((row, index) => {
        const dbName = row.datname
        colorLog(`${(index + 1).toString().padStart(2)}. ${dbName}`, 'bright')
      })
    }
    
    // 询问用户操作方式
    colorLog('\n选择操作方式:', 'cyan')
    colorLog('1. 使用现有数据库', 'bright')
    colorLog('2. 创建新数据库', 'bright')
    colorLog('0. 取消', 'bright')
    
    const operationChoice = await askQuestion('\n请选择操作 (0-2): ')
    
    if (operationChoice === '0') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }
    
    let targetDb = ''
    let targetUser = ''
    let targetPassword = ''
    
    if (operationChoice === '1') {
      // 使用现有数据库
      if (allDbResult.rows.length === 0) {
        colorLog('❌ 没有可用的数据库', 'red')
        await client.end()
        return
      }
      
      const dbChoice = await askQuestion('\n请选择数据库编号 (0 取消): ')
      if (dbChoice === '0') {
        colorLog('操作已取消', 'yellow')
        await client.end()
        return
      }
      
      const dbIndex = parseInt(dbChoice) - 1
      if (isNaN(dbIndex) || dbIndex < 0 || dbIndex >= allDbResult.rows.length) {
        colorLog('❌ 无效的选择', 'red')
        await client.end()
        return
      }
      
      targetDb = allDbResult.rows[dbIndex].datname
      
      // 检查用户是否存在
      const userResult = await client.query(`
        SELECT usename 
        FROM pg_user 
        WHERE usename = $1
      `, [targetDb])
      
      if (userResult.rows.length > 0) {
        targetUser = targetDb
        colorLog(`✅ 用户 "${targetUser}" 已存在`, 'green')
      } else {
        targetUser = targetDb
        colorLog(`⚠️  用户 "${targetUser}" 不存在，将创建新用户`, 'yellow')
        
        // 获取密码
        while (!targetPassword) {
          targetPassword = await askQuestion(`请为用户 "${targetUser}" 设置密码: `)
          if (!targetPassword) {
            colorLog('❌ 密码不能为空', 'red')
          }
        }
      }
      
      colorLog(`\n⚠️  警告：即将初始化数据库 "${targetDb}"`, 'yellow')
      colorLog('⚠️  此操作可能会修改数据库结构，但不会删除现有数据', 'yellow')
      
    } else if (operationChoice === '2') {
      // 创建新数据库
      while (!targetDb) {
        targetDb = await askQuestion('请输入新数据库名称: ')
        if (!targetDb) {
          colorLog('❌ 数据库名称不能为空', 'red')
          continue
        }
        
        if (!/^[a-z0-9_]+$/.test(targetDb)) {
          colorLog('❌ 数据库名称只能包含小写字母、数字和下划线', 'red')
          targetDb = ''
          continue
        }
        
        // 检查数据库是否已存在
        const dbExists = await client.query(`SELECT 1 FROM pg_database WHERE datname = $1`, [targetDb])
        if (dbExists.rows.length > 0) {
          colorLog(`❌ 数据库 "${targetDb}" 已存在`, 'red')
          targetDb = ''
          continue
        }
      }
      
      // 获取用户名（默认与数据库名相同）
      targetUser = await askQuestion(`请输入数据库用户名 (默认: ${targetDb}): `) || targetDb
      
      // 获取密码
      while (!targetPassword) {
        targetPassword = await askQuestion(`请为用户 "${targetUser}" 设置密码: `)
        if (!targetPassword) {
          colorLog('❌ 密码不能为空', 'red')
        }
      }
      
      colorLog(`\n📋 即将创建以下数据库:`, 'cyan')
      colorLog(`🗄️  数据库名称: ${targetDb}`, 'bright')
      colorLog(`👤 数据库用户: ${targetUser}`, 'bright')
      colorLog(`🔑 数据库密码: ${'*'.repeat(targetPassword.length)}`, 'bright')
      
    } else {
      colorLog('❌ 无效的选择', 'red')
      await client.end()
      return
    }
    
    // 最终确认
    const confirm = await askQuestion('\n确认继续? (y/N): ')
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      colorLog('操作已取消', 'yellow')
      await client.end()
      return
    }
    
    // 开始初始化
    colorLog('\n🚀 开始初始化数据库...', 'cyan')
    
    // 如果是新数据库，先创建
    if (operationChoice === '2') {
      try {
        await client.query(`CREATE DATABASE ${targetDb}`)
        colorLog(`✅ 已创建数据库 "${targetDb}"`, 'green')
      } catch (error) {
        colorLog(`❌ 创建数据库失败: ${error.message}`, 'red')
        await client.end()
        return
      }
    }
    
    // 创建用户（如果不存在）
    try {
      // 检查用户是否存在
      const userExists = await client.query(`SELECT 1 FROM pg_user WHERE usename = $1`, [targetUser])
      
      if (userExists.rows.length === 0) {
        await client.query(`CREATE USER ${targetUser} WITH PASSWORD '${targetPassword}'`)
        colorLog(`✅ 已创建用户 "${targetUser}"`, 'green')
      } else if (targetPassword) {
        // 如果用户已存在且提供了密码，更新密码
        await client.query(`ALTER USER ${targetUser} WITH PASSWORD '${targetPassword}'`)
        colorLog(`✅ 已更新用户 "${targetUser}" 的密码`, 'green')
      }
    } catch (error) {
      colorLog(`⚠️  用户操作失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }
    
    // 授予权限
    try {
      await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${targetDb} TO ${targetUser}`)
      colorLog(`✅ 已授予数据库权限给 "${targetUser}"`, 'green')
    } catch (error) {
      colorLog(`⚠️  授权失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }
    
    await client.end()
    
    // 连接到目标数据库设置 schema 权限
    try {
      const dbClient = new Client({
        host: DB_CONFIG.host,
        port: DB_CONFIG.port,
        user: DB_CONFIG.superUser,
        password: DB_CONFIG.superPassword,
        database: targetDb,
      })
      
      await dbClient.connect()
      await dbClient.query(`GRANT ALL ON SCHEMA public TO ${targetUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${targetUser}`)
      await dbClient.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${targetUser}`)
      colorLog(`✅ 已设置 schema 权限`, 'green')
      await dbClient.end()
    } catch (error) {
      colorLog(`⚠️  设置 schema 权限失败: ${error.message}`, 'yellow')
      // 继续执行，不中断流程
    }
    
    // 更新配置
    DB_CONFIG.databaseName = targetDb
    DB_CONFIG.databaseUser = targetUser
    if (targetPassword) {
      DB_CONFIG.databasePassword = targetPassword
    }
    
    // 询问是否更新 .env 文件
    const updateEnv = await askQuestion('\n是否更新 .env 文件? (Y/n): ')
    if (updateEnv.toLowerCase() !== 'n' && updateEnv.toLowerCase() !== 'no') {
      updateEnvFile()
    }
    
    // 询问是否保存配置
    const saveConfigChoice = await askQuestion('\n是否保存当前配置? (Y/n): ')
    if (saveConfigChoice.toLowerCase() !== 'n' && saveConfigChoice.toLowerCase() !== 'no') {
      const savePasswords = await askQuestion('是否保存密码? (y/N): ')
      
      // 临时设置环境变量以控制是否保存密码
      if (savePasswords.toLowerCase() === 'y' || savePasswords.toLowerCase() === 'yes') {
        process.env.DBM_SAVE_PASSWORDS = 'true'
      }
      
      saveConfig()
      
      // 清除环境变量
      delete process.env.DBM_SAVE_PASSWORDS
    }
    
    colorLog('\n🎉 数据库初始化完成！', 'green')
    
    // 显示连接字符串
    const connectionString = `postgres://${targetUser}:${targetPassword || DB_CONFIG.databasePassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${targetDb}`
    colorLog('\n🔗 数据库连接字符串:', 'cyan')
    colorLog(`${connectionString}`, 'yellow')
    
  } catch (error) {
    colorLog(`❌ 数据库初始化失败: ${error.message}`, 'red')
    throw error
  } finally {
    if (client.connected) {
      await client.end().catch(() => {})
    }
  }
}

// 数据库重建
async function rebuildDatabase() {
  colorLog('\n⚠️  警告：此操作将删除所有数据！', 'red')

  const answer = await askQuestion('确定要重建数据库吗？这将删除所有现有数据！(y/N): ')
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    colorLog('操作已取消', 'yellow')
    return
  }

  colorLog('\n🔄 开始重建数据库...', 'cyan')

  const client = new Client({
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.superUser,
    password: DB_CONFIG.superPassword,
    database: DB_CONFIG.defaultDatabase,
  })

  try {
    await client.connect()

    // 断开所有连接到目标数据库的连接
    await client.query(
      `
      SELECT pg_terminate_backend(pid)
      FROM pg_stat_activity
      WHERE datname = $1 AND pid <> pg_backend_pid()
    `,
      [DB_CONFIG.databaseName],
    )

    // 删除数据库
    try {
      await client.query(`DROP DATABASE IF EXISTS ${DB_CONFIG.databaseName}`)
      colorLog(`✅ 已删除数据库 "${DB_CONFIG.databaseName}"`, 'green')
    } catch (error) {
      colorLog(`⚠️  删除数据库时出现问题: ${error.message}`, 'yellow')
    }

    // 重新创建数据库
    await client.query(`CREATE DATABASE ${DB_CONFIG.databaseName}`)
    colorLog(`✅ 已重新创建数据库 "${DB_CONFIG.databaseName}"`, 'green')

    // 授予权限
    await client.query(
      `GRANT ALL PRIVILEGES ON DATABASE ${DB_CONFIG.databaseName} TO ${DB_CONFIG.databaseUser}`,
    )
    colorLog(`✅ 已重新授予权限`, 'green')

    await client.end()

    // 设置 schema 权限
    const dbClient = new Client({
      host: DB_CONFIG.host,
      port: DB_CONFIG.port,
      user: DB_CONFIG.superUser,
      password: DB_CONFIG.superPassword,
      database: DB_CONFIG.databaseName,
    })

    await dbClient.connect()
    await dbClient.query(`GRANT ALL ON SCHEMA public TO ${DB_CONFIG.databaseUser}`)
    await dbClient.query(
      `GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${DB_CONFIG.databaseUser}`,
    )
    await dbClient.query(
      `GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${DB_CONFIG.databaseUser}`,
    )
    await dbClient.end()

    colorLog('🎉 数据库重建完成！', 'green')
  } catch (error) {
    colorLog(`❌ 数据库重建失败: ${error.message}`, 'red')
    throw error
  }
}

// 运行数据库迁移
async function runMigrations() {
  colorLog('\n🔄 开始运行数据库迁移...', 'cyan')

  try {
    execSync('pnpm payload migrate', { stdio: 'inherit', cwd: process.cwd() })
    colorLog('✅ 数据库迁移完成！', 'green')
  } catch (error) {
    colorLog(`❌ 数据库迁移失败: ${error.message}`, 'red')
    throw error
  }
}

// 更新 .env 文件
function updateEnvFile() {
  try {
    const envPath = path.join(process.cwd(), '.env')
    let envContent = fs.readFileSync(envPath, 'utf8')

    const newDatabaseUri = `postgres://${DB_CONFIG.databaseUser}:${DB_CONFIG.databasePassword}@${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.databaseName}`

    envContent = envContent.replace(/DATABASE_URI=.*/, `DATABASE_URI=${newDatabaseUri}`)

    fs.writeFileSync(envPath, envContent)
    colorLog('✅ 已更新 .env 文件', 'green')
  } catch (error) {
    colorLog(`⚠️  更新 .env 文件时出现问题: ${error.message}`, 'yellow')
  }
}

// 主程序
async function main() {
  try {
    // 初始化配置
    await initializeConfig()
    
    while (true) {
      showMenu()
      const choice = await askQuestion('请选择操作 (0-8): ')

      switch (choice) {
        case '1':
          await testConnection()
          break
        case '2':
          await initializeDatabase()
          break
        case '3':
          await rebuildDatabase()
          break
        case '4':
          await runMigrations()
          break
        case '5':
          await showConfig(true) // 显示完整配置，包括密码
          break
        case '6':
          await deleteDatabase()
          break
        case '7':
          await createNewDatabase()
          break
        case '8':
          await initializeConfig() // 重新配置
          break
        case '0':
          colorLog('\n👋 再见！', 'green')
          rl.close()
          return
        default:
          colorLog('\n❌ 无效选择，请重新输入', 'red')
      }

      if (choice !== '0') {
        await askQuestion('\n按 Enter 键继续...')
      }
    }
  } catch (error) {
    colorLog(`\n💥 程序出现错误: ${error.message}`, 'red')
    rl.close()
    process.exit(1)
  }
}

// 启动程序
if (require.main === module) {
  main()
}



第一项目：数据库检测是用来检查当前硬编码的主数据库是否能正常连接，然后列出连接信息即可；
第二项：数据库初始化，直接列出当前数据库中存在的数据库让我选择即可，无需添加新建或使用现有的选项；
第三项：数据库重建，直接列出当前数据库中存在的数据库让我选择即可完成重建保留警告信息；
第四项：数据库迁移，直接列出当前数据库中存在的数据库让我选择即可完成迁移保留警告信息；
第五项：数据库删除，列出当前数据库中存在的数据库让我选择即可完成删除保留警告信息；
第六项：数据库新建，输入数据库名称、密码(可生成一个默认密码)等信息完成新建，并提示是否更新环境变量或新增环境变量
第七项：数据库配置修改，列出当前数据库配置信息，并提示是否更新环境变量或新增环境变量
第0项：退出脚本
